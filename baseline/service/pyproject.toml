[build-system]
build-backend = "hatchling.build"
requires = ["hatchling"]

[project]
authors = [{name = "ML Platform Team"}]
dependencies = [
  "aioboto3>=14.1.0",
  "gametime-protos==0.7.0",
  "grpcio>=1.71.0",
  "mlutils[grpc,aiohttp]>=1.0.0",
  "nbformat>=5.10.0",
  "pendulum>=3.0.0"
]
description = "Development Platform"
name = "baseline-service"
requires-python = ">=3.10,<3.12"
version = "1.7.0"

[project.optional-dependencies]
dev = [
  "pytest-asyncio>=0.25.3",
  "pytest-httpserver>=1.1.2",
  "pytest>=8.3.5",
  "testcontainers[minio]>=4.9.2"
]

[tool.hatch.build.targets.wheel]
packages = ["."]

[tool.hatch.metadata]
allow-direct-references = true

[tool.semantic_release]
build_command = ""
changelog_file = "CHANGELOG.md"
commit_parser = "conventional"
tag_format = "baseline-service-v{version}"
upload_to_release = true
upload_to_repository = false
version_toml = ["pyproject.toml:project.version"]

[tool.semantic_release.branches]
main.match = "main"

[[tool.uv.index]]
name = "codeartifact"
url = "https://ml-artifacts-728489771660.d.codeartifact.us-west-2.amazonaws.com/pypi/python-packages/simple/"

[tool.uv.sources]
gametime-protos = {index = "codeartifact"}
mlutils = {index = "codeartifact"}
